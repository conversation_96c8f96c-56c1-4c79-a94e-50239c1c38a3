import { StyleSheet, View, Text } from "react-native";
import Phases from "./Phases";
import DailyLifts from "./DailyLifts";

const LandingContent = () => {
  return (
    <View style={styles.container}>
      <Phases />
      <DailyLifts />
    </View>
  );
};

export default LandingContent;

const styles = StyleSheet.create({
  container: {
    marginTop: 80,
    flex: 1,
    width: "100%",
    alignItems: "flex-start",
    justifyContent: "flex-start",
    gap: 72,
  },
});
