import { Text, StyleSheet, Image, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const EditPhasesButton = () => {
  return (
    <SafeAreaView style={styles.container}>
      <TouchableOpacity style={styles.buttonContainer}>
        <Image
          source={require("../../../assets/edit-button.png")}
          style={styles.editIcon}
        />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default EditPhasesButton;

const styles = StyleSheet.create({
  container: {
    alignSelf: "flex-end",
  },
  buttonContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  editIcon: {
    width: 32,
    height: 32,
  },
});
