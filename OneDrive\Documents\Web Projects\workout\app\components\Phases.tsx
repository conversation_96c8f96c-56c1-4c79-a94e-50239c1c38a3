import { Button, StyleSheet, Text, View } from "react-native";
import EditPhasesButton from "./EditPhasesButton";

interface PhasesProps {
  week?: number;
  day?: number;
  phase?: string;
  repRange?: string;
}

const Phases = ({
  week = 2,
  day = 5,
  phase = "80%",
  repRange = "6-10",
}: PhasesProps) => {
  return (
    <View className="flex content-container" style={styles.container}>
      <EditPhasesButton />
      <Text className="font-roboto-light text-primary" style={styles.date}>
        Week {week}: Day {day}
      </Text>
      <Text className="font-roboto-black text-primary" style={styles.phase}>
        Phase: {phase}
      </Text>
      <Text
        className="font-roboto-medium text-2xl text-primary"
        style={styles.repRange}
      >
        Rep Range: {repRange}
      </Text>
    </View>
  );
};

export default Phases;

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignItems: "flex-start",
    justifyContent: "flex-start",
  },
  date: {
    fontSize: 20,
  },
  phase: {
    fontSize: 56,
    marginTop: -16,
  },
  repRange: {
    fontSize: 24,
    marginTop: -4,
  },
});
