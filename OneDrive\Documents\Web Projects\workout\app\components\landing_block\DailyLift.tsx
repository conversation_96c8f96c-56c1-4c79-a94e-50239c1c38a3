import { StyleSheet, View, Text } from "react-native";

interface DailyLiftProps {
  lift?: string;
  weight?: number;
  reps?: number;
  date?: Date;
}

const DailyLift = ({
  lift = "Deadlift",
  weight = 425,
  reps = 30,
  date = new Date(),
}: DailyLiftProps) => {
  // Dynamic font size based on text length
  const getFontSize = (text: string) => {
    if (text.length <= 8) return 20;
    if (text.length <= 12) return 18;
    if (text.length <= 16) return 16;
    return 14;
  };

  return (
    <View style={styles.container} className="bg-secondary">
      <Text
        className="font-roboto-medium text-primary"
        style={[styles.liftName, { fontSize: getFontSize(lift) }]}
      >
        {lift}
      </Text>
      <View style={styles.weightContainer}>
        <Text style={styles.weight} className="font-roboto-black text-primary">
          {weight}
        </Text>
        <Text style={styles.lbs} className="font-roboto-light text-primary">
          lbs
        </Text>
      </View>
      <Text
        className="font-roboto-light text-primary"
        style={styles.reps}
      >{`Reps: ${reps}`}</Text>
      <View style={styles.dateContainer}>
        <Text
          className="font-roboto-light text-primary"
          style={styles.date}
        >{`${date.toLocaleDateString()}`}</Text>
      </View>
    </View>
  );
};

export default DailyLift;

const styles = StyleSheet.create({
  container: {
    width: 122,
    height: 168,
    display: "flex",
    alignItems: "center",
    borderRadius: 8,
    justifyContent: "flex-start",

    marginRight: 20,
    boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
    paddingHorizontal: 4,
    paddingVertical: 8,
  },
  liftName: {
    textAlign: "center",
    lineHeight: 30,
  },
  weightContainer: {
    marginTop: 4,
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    paddingStart: 11,
    borderBottomColor: "#212122",
    borderBottomWidth: 2,
    paddingBottom: 0,
  },
  weight: {
    fontSize: 32,
  },
  lbs: {
    fontSize: 12,
    marginTop: 4,
    alignSelf: "flex-start",
  },
  reps: {
    fontSize: 20,
    marginTop: 4,
  },
  dateContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  date: {
    fontSize: 16,
    marginTop: 4,

    marginBottom: 0,
  },
});
