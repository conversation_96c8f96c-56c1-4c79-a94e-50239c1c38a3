import { StyleSheet, View, Text, Image } from "react-native";

import { SafeAreaView } from "react-native-safe-area-context";
import BoxTransition from "./BoxTransition";

import LandingContent from "./LandingContent";

const LandingBackdrop = () => {
  return (
    <SafeAreaView style={styles.container}>
      <LandingContent />
      <BoxTransition />
      <View style={styles.backdropContainer}>
        <Image
          source={require("../../../assets/background-weight.png")}
          style={styles.backdropImage}
        />
      </View>
    </SafeAreaView>
  );
};

export default LandingBackdrop;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    display: "flex",
    alignItems: "flex-start",
    justifyContent: "flex-end",
    width: "100%",
    gap: 52,
  },
  backdropContainer: {
    flex: 1,
    position: "absolute",

    justifyContent: "center",
    alignItems: "center",
    width: "100%",
    height: "100%",
    zIndex: -1,
  },
  backdropImage: {
    alignSelf: "flex-end",
    marginRight: 24,
    position: "relative",
    top: -32,
  },
});
