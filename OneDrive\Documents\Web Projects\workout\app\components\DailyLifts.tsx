import { StyleSheet, View, Text, ScrollView } from "react-native";
import DailyLift from "./DailyLift";

const testLifts = [
  "Back Squat",
  "Deadlift",
  "Sumo Deadlift",
  "Front Squat",
  "Arms",
];

const DailyLifts = ({ lift = "Leg" }: { lift?: string }) => {
  return (
    <View style={styles.container} className="flex">
      <Text
        className="font-roboto-black text-primary"
        style={styles.lift_title}
      >
        {lift} Day Lifts
      </Text>
      <ScrollView
        horizontal={true}
        className="flex content-container"
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsHorizontalScrollIndicator={false}
      >
        {testLifts.map((lift, index) => (
          <DailyLift key={index} lift={lift} />
        ))}
      </ScrollView>
    </View>
  );
};

export default DailyLifts;

const styles = StyleSheet.create({
  container: {
    display: "flex",
    width: "100%",
    alignItems: "flex-start",
    justifyContent: "flex-start",
  },
  lift_title: {
    fontSize: 48,
    marginStart: 24,
    marginBottom: 8,
  },
  scrollView: {
    display: "flex",
    width: "100%",
    overflow: "visible",
    flexDirection: "row",
    paddingHorizontal: 24,
    marginEnd: 24,
  },
  scrollContent: {
    flexDirection: "row",
    alignItems: "center",
    paddingRight: 24,
    paddingBottom: 8,
  },
});
