/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all files that contain Nativewind classes.
  content: ["./app/**/*.{js,jsx,ts,tsx}", "./components/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        primary: "#212122",
        secondary: "#E0E0DD",
      },
      fontFamily: {
        // Roboto font family
        "roboto-thin": ["Roboto-Thin", "sans-serif"],
        "roboto-light": ["Roboto-Light", "sans-serif"],
        roboto: ["Roboto-Regular", "sans-serif"],
        "roboto-medium": ["Roboto-Medium", "sans-serif"],
        "roboto-bold": ["Roboto-Bold", "sans-serif"],
        "roboto-black": ["Roboto-Black", "sans-serif"],

        // Semantic font names for your app
        heading: ["Roboto-Bold", "sans-serif"],
        subheading: ["Roboto-Medium", "sans-serif"],
        body: ["Roboto-Regular", "sans-serif"],
        caption: ["Roboto-Light", "sans-serif"],
      },
    },
  },
  plugins: [],
};
