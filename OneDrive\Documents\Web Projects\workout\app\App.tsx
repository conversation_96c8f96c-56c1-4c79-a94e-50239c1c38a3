import { StatusBar } from "expo-status-bar";
import { StyleSheet, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";

import { useEffect } from "react";
import "./global.css";

import Phases from "./components/Phases";
import { useFonts } from "./hooks/useFonts";
import LandingBackdrop from "./components/LandingBackdrop";
import BoxTransition from "./components/BoxTransition";

export default function App() {
  const fontsLoaded = useFonts();

  if (!fontsLoaded) {
    return null;
  }

  return (
    <View style={styles.container} className="bg-secondary">
      <LinearGradient
        colors={["#F3ADAA", "#9EA8EE"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        locations={[0.05, 0.95]}
        className="w-full"
        style={styles.linearGradient}
      >
        <LandingBackdrop />
      </LinearGradient>
      <SafeAreaView style={styles.contentContainer}></SafeAreaView>
      <StatusBar style="dark" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  contentContainer: {
    flex: 1,
    alignItems: "flex-start",
    backgroundColor: "transparent",
    justifyContent: "flex-start",
  },
  linearGradient: {
    height: 725,
  },
});
