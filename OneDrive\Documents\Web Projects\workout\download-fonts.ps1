# PowerShell script to download Roboto fonts
# Run this script from the project root directory

Write-Host "Downloading Roboto fonts..." -ForegroundColor Green

# Create fonts directory if it doesn't exist
if (!(Test-Path "assets/fonts")) {
    New-Item -ItemType Directory -Path "assets/fonts" -Force
    Write-Host "Created assets/fonts directory" -ForegroundColor Yellow
}

# Roboto font URLs from Google Fonts GitHub repository
$fonts = @{
    "Roboto-Thin.ttf" = "https://github.com/google/fonts/raw/main/apache/roboto/Roboto-Thin.ttf"
    "Roboto-Light.ttf" = "https://github.com/google/fonts/raw/main/apache/roboto/Roboto-Light.ttf"
    "Roboto-Regular.ttf" = "https://github.com/google/fonts/raw/main/apache/roboto/Roboto-Regular.ttf"
    "Roboto-Medium.ttf" = "https://github.com/google/fonts/raw/main/apache/roboto/Roboto-Medium.ttf"
    "Roboto-Bold.ttf" = "https://github.com/google/fonts/raw/main/apache/roboto/Roboto-Bold.ttf"
    "Roboto-Black.ttf" = "https://github.com/google/fonts/raw/main/apache/roboto/Roboto-Black.ttf"
}

foreach ($font in $fonts.GetEnumerator()) {
    $fileName = $font.Key
    $url = $font.Value
    $outputPath = "assets/fonts/$fileName"
    
    Write-Host "Downloading $fileName..." -ForegroundColor Cyan
    
    try {
        Invoke-WebRequest -Uri $url -OutFile $outputPath -UseBasicParsing
        Write-Host "✓ Downloaded $fileName" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to download $fileName" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nFont download complete!" -ForegroundColor Green
Write-Host "You can now run 'npm start' to test your fonts." -ForegroundColor Yellow

# List downloaded fonts
Write-Host "`nDownloaded fonts:" -ForegroundColor Cyan
Get-ChildItem "assets/fonts/*.ttf" | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor White }
