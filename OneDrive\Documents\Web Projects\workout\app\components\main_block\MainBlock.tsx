import { SafeAreaView } from "react-native-safe-area-context";
import { StyleSheet } from "react-native";
import LiftButton from "./LiftButton";

const MainBlock = () => {
  return (
    <SafeAreaView style={styles.container}>
      <LiftButton />
    </SafeAreaView>
  );
};

export default MainBlock;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    display: "flex",
    alignItems: "flex-start",
    justifyContent: "flex-start",
    width: "100%",
  },
});
