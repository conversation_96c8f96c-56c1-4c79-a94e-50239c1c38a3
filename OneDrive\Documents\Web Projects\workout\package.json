{"name": "workout", "version": "1.0.0", "main": "./app/index.tsx", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.13", "expo-splash-screen": "^0.30.9", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.4", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "expo-linear-gradient": "~14.1.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "@react-native-community/cli": "latest"}, "private": true}